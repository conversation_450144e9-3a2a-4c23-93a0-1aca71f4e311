import { useState, useEffect } from 'react';

export interface FilterOption {
  value: string;
  label: string;
  count: number;
}

interface FilterOptionsData {
  platforms: FilterOption[];
  merchants: FilterOption[];
  statuses: FilterOption[];
}

interface UseFilterOptionsReturn {
  data: FilterOptionsData | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useFilterOptions(): UseFilterOptionsReturn {
  const [data, setData] = useState<FilterOptionsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchFilterOptions = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/transactions/filters');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.error) {
        throw new Error(result.error);
      }

      setData(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch filter options';
      setError(errorMessage);
      console.error('Error fetching filter options:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFilterOptions();
  }, []);

  return {
    data,
    loading,
    error,
    refetch: fetchFilterOptions,
  };
}
