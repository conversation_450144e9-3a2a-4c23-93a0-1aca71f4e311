"use client";

import * as React from "react";
import { CalendarIcon, FilterIcon, XIcon } from "lucide-react";
import { format } from "date-fns";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useFilterOptions } from "@/hooks/useFilterOptions";

interface TransactionFiltersProps {
  onFiltersChange: (filters: {
    platform?: string;
    merchant?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
  }) => void;
  initialFilters?: {
    platform?: string;
    merchant?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
  };
}

export function TransactionFilters({ onFiltersChange, initialFilters = {} }: TransactionFiltersProps) {
  // Fetch dynamic filter options
  const { data: filterOptions, loading: filterOptionsLoading, error: filterOptionsError } = useFilterOptions();
  const [filters, setFilters] = React.useState(initialFilters);
  const [startDate, setStartDate] = React.useState<Date | undefined>(
    initialFilters.startDate ? new Date(initialFilters.startDate) : undefined
  );
  const [endDate, setEndDate] = React.useState<Date | undefined>(
    initialFilters.endDate ? new Date(initialFilters.endDate) : undefined
  );

  // Update filters when they change
  React.useEffect(() => {
    const updatedFilters = {
      ...filters,
      startDate: startDate ? format(startDate, "yyyy-MM-dd") : undefined,
      endDate: endDate ? format(endDate, "yyyy-MM-dd") : undefined,
    };
    onFiltersChange(updatedFilters);
  }, [filters.platform, filters.merchant, filters.status, startDate, endDate]); // Remove onFiltersChange from deps

  const handleFilterChange = (key: string, value: string | undefined) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined
    }));
  };

  const clearFilters = () => {
    setFilters({});
    setStartDate(undefined);
    setEndDate(undefined);
  };

  const activeFiltersCount = Object.values({
    ...filters,
    startDate,
    endDate
  }).filter(Boolean).length;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <FilterIcon className="h-5 w-5" />
              Filters
            </CardTitle>
            <CardDescription>
              Filter transactions by platform, merchant, status, and date range
              {filterOptionsError && (
                <span className="text-red-600 block mt-1">
                  Error loading filter options: {filterOptionsError}
                </span>
              )}
            </CardDescription>
          </div>
          {activeFiltersCount > 0 && (
            <div className="flex items-center gap-2">
              <Badge variant="secondary">
                {activeFiltersCount} filter{activeFiltersCount !== 1 ? 's' : ''} active
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={clearFilters}
              >
                <XIcon className="h-4 w-4 mr-1" />
                Clear
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {/* Platform Filter */}
          <div className="space-y-2">
            <Label htmlFor="platform">Platform</Label>
            <Select
              value={filters.platform || undefined}
              onValueChange={(value) => handleFilterChange("platform", value === "all" ? undefined : value)}
              disabled={filterOptionsLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder={filterOptionsLoading ? "Loading..." : "All platforms"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All platforms</SelectItem>
                {filterOptions?.platforms.map((platform) => (
                  <SelectItem key={platform.value} value={platform.value}>
                    {platform.label} ({platform.count})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Merchant Filter */}
          <div className="space-y-2">
            <Label htmlFor="merchant">Merchant</Label>
            <Select
              value={filters.merchant || undefined}
              onValueChange={(value) => handleFilterChange("merchant", value === "all" ? undefined : value)}
              disabled={filterOptionsLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder={filterOptionsLoading ? "Loading..." : "All merchants"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All merchants</SelectItem>
                {filterOptions?.merchants.map((merchant) => (
                  <SelectItem key={merchant.value} value={merchant.value}>
                    {merchant.label} ({merchant.count})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Status Filter */}
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={filters.status || undefined}
              onValueChange={(value) => handleFilterChange("status", value === "all" ? undefined : value)}
              disabled={filterOptionsLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder={filterOptionsLoading ? "Loading..." : "All statuses"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All statuses</SelectItem>
                {filterOptions?.statuses.map((status) => (
                  <SelectItem key={status.value} value={status.value}>
                    {status.label} ({status.count})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Start Date Filter */}
          <div className="space-y-2">
            <Label>Start Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {startDate ? format(startDate, "PPP") : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={startDate}
                  onSelect={setStartDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* End Date Filter */}
          <div className="space-y-2">
            <Label>End Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {endDate ? format(endDate, "PPP") : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={endDate}
                  onSelect={setEndDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
